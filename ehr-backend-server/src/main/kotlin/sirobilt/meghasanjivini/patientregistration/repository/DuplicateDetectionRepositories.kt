package sirobilt.meghasanjivini.patientregistration.repository

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepositoryBase
import io.quarkus.panache.common.Page
import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import sirobilt.meghasanjivini.patientregistration.model.DuplicateDetectionConfigEntity
import sirobilt.meghasanjivini.patientregistration.model.DuplicateDetectionLogEntity
import sirobilt.meghasanjivini.patientregistration.model.DuplicatePatientRelationshipEntity
import sirobilt.meghasanjivini.patientregistration.model.IdentifierType
import sirobilt.meghasanjivini.patientregistration.model.Patient
import java.time.LocalDate
import java.time.OffsetDateTime

/**
 * Repository for duplicate detection configuration
 */
@ApplicationScoped
class DuplicateDetectionConfigRepository : PanacheRepositoryBase<DuplicateDetectionConfigEntity, Long> {

    /**
     * Find configuration by key
     */
    fun findByKey(key: String): DuplicateDetectionConfigEntity? {
        return find("configKey = ?1 AND isActive = true", key).firstResult()
    }

    /**
     * Find all active configurations
     */
    fun findAllActive(): List<DuplicateDetectionConfigEntity> {
        return find("isActive = true").list()
    }

    /**
     * Update configuration value
     */
    @Transactional
    fun updateConfigValue(key: String, value: String): Boolean {
        val updated = update("configValue = ?1, updatedAt = ?2 WHERE configKey = ?3 AND isActive = true", 
                           value, OffsetDateTime.now(), key)
        return updated > 0
    }
}

/**
 * Repository for duplicate detection audit logs
 */
@ApplicationScoped
class DuplicateDetectionLogRepository : PanacheRepositoryBase<DuplicateDetectionLogEntity, Long> {

    /**
     * Find logs by patient ID
     */
    fun findByPatientId(patientId: String): List<DuplicateDetectionLogEntity> {
        return find("patientId = ?1 ORDER BY detectionTime DESC", patientId).list()
    }

    /**
     * Find logs by confidence level
     */
    fun findByConfidenceLevel(confidenceLevel: String, page: Int, size: Int): List<DuplicateDetectionLogEntity> {
        return find("confidenceLevel = ?1 ORDER BY detectionTime DESC", confidenceLevel)
            .page(Page.of(page, size))
            .list()
    }

    /**
     * Find logs within date range
     */
    fun findByDateRange(from: OffsetDateTime, to: OffsetDateTime): List<DuplicateDetectionLogEntity> {
        return find("detectionTime >= ?1 AND detectionTime <= ?2 ORDER BY detectionTime DESC", from, to).list()
    }

    /**
     * Find logs requiring review (flagged but not reviewed)
     */
    fun findPendingReview(): List<DuplicateDetectionLogEntity> {
        return find("actionTaken = 'FLAGGED' AND reviewTime IS NULL ORDER BY detectionTime ASC").list()
    }

    /**
     * Count logs by detection type
     */
    fun countByDetectionType(detectionType: String): Long {
        return count("detectionType = ?1", detectionType)
    }

    /**
     * Find recent high-confidence duplicates
     */
    fun findRecentHighConfidenceDuplicates(days: Int): List<DuplicateDetectionLogEntity> {
        val cutoffDate = OffsetDateTime.now().minusDays(days.toLong())
        return find("confidenceLevel = 'HIGH' AND detectionTime >= ?1 ORDER BY detectionTime DESC", cutoffDate).list()
    }

    /**
     * Get duplicate detection statistics
     */
    fun getDetectionStatistics(from: OffsetDateTime, to: OffsetDateTime): Map<String, Long> {
        val stats = mutableMapOf<String, Long>()
        
        stats["total"] = count("detectionTime >= ?1 AND detectionTime <= ?2", from, to)
        stats["high_confidence"] = count("detectionTime >= ?1 AND detectionTime <= ?2 AND confidenceLevel = 'HIGH'", from, to)
        stats["medium_confidence"] = count("detectionTime >= ?1 AND detectionTime <= ?2 AND confidenceLevel = 'MEDIUM'", from, to)
        stats["blocked"] = count("detectionTime >= ?1 AND detectionTime <= ?2 AND actionTaken = 'BLOCKED'", from, to)
        stats["flagged"] = count("detectionTime >= ?1 AND detectionTime <= ?2 AND actionTaken = 'FLAGGED'", from, to)
        stats["approved"] = count("detectionTime >= ?1 AND detectionTime <= ?2 AND actionTaken = 'APPROVED'", from, to)
        
        return stats
    }
}

/**
 * Repository for duplicate patient relationships
 */
@ApplicationScoped
class DuplicatePatientRelationshipRepository : PanacheRepositoryBase<DuplicatePatientRelationshipEntity, Long> {

    /**
     * Find relationships by primary patient ID
     */
    fun findByPrimaryPatientId(primaryPatientId: String): List<DuplicatePatientRelationshipEntity> {
        return find("primaryPatientId = ?1 AND isActive = true ORDER BY identifiedAt DESC", primaryPatientId).list()
    }

    /**
     * Find relationships by duplicate patient ID
     */
    fun findByDuplicatePatientId(duplicatePatientId: String): List<DuplicatePatientRelationshipEntity> {
        return find("duplicatePatientId = ?1 AND isActive = true ORDER BY identifiedAt DESC", duplicatePatientId).list()
    }

    /**
     * Check if relationship exists between two patients
     */
    fun relationshipExists(patientId1: String, patientId2: String): Boolean {
        val count = count(
            "(primaryPatientId = ?1 AND duplicatePatientId = ?2) OR (primaryPatientId = ?2 AND duplicatePatientId = ?1) AND isActive = true",
            patientId1, patientId2
        )
        return count > 0
    }

    /**
     * Find confirmed duplicates
     */
    fun findConfirmedDuplicates(): List<DuplicatePatientRelationshipEntity> {
        return find("relationshipType = 'CONFIRMED_DUPLICATE' AND isActive = true ORDER BY identifiedAt DESC").list()
    }

    /**
     * Find false positives
     */
    fun findFalsePositives(): List<DuplicatePatientRelationshipEntity> {
        return find("relationshipType = 'FALSE_POSITIVE' AND isActive = true ORDER BY identifiedAt DESC").list()
    }

    /**
     * Get all related patients for a given patient ID
     */
    fun findAllRelatedPatients(patientId: String): List<String> {
        val relationships = find(
            "(primaryPatientId = ?1 OR duplicatePatientId = ?1) AND relationshipType = 'CONFIRMED_DUPLICATE' AND isActive = true",
            patientId
        ).list()

        return relationships.map { relationship ->
            if (relationship.primaryPatientId == patientId) {
                relationship.duplicatePatientId
            } else {
                relationship.primaryPatientId
            }
        }.distinct()
    }

    /**
     * Deactivate relationship
     */
    @Transactional
    fun deactivateRelationship(relationshipId: Long): Boolean {
        val updated = update("isActive = false WHERE relationshipId = ?1", relationshipId)
        return updated > 0
    }
}

/**
 * Extended patient repository with duplicate detection queries
 */
@ApplicationScoped
class PatientDuplicateRepository : PanacheRepositoryBase<Patient, String> {

    /**
     * Find potential duplicates based on name and date of birth
     * Uses a more lenient approach to find candidates for fuzzy matching
     */
    fun findPotentialDuplicatesByNameAndDob(
        firstName: String?,
        lastName: String?,
        dateOfBirth: LocalDate?
    ): List<Patient> {
        if (firstName.isNullOrBlank() && lastName.isNullOrBlank() && dateOfBirth == null) {
            return emptyList()
        }

        val query = StringBuilder("softDeleted = false")
        val params = mutableMapOf<String, Any>()
        val nameConditions = mutableListOf<String>()

        // Build more flexible name matching conditions for fuzzy matching
        // We want to cast a wider net to find potential candidates, then let the fuzzy matching engine
        // do the detailed similarity analysis
        if (!firstName.isNullOrBlank()) {
            // More lenient matching: exact match, contains, starts with, and similar length
            nameConditions.add("""(
                LOWER(firstName) = LOWER(:firstName)
                OR LOWER(firstName) LIKE LOWER(:firstNameFuzzy)
                OR LOWER(:firstName) LIKE LOWER(CONCAT('%', firstName, '%'))
                OR LOWER(firstName) LIKE LOWER(CONCAT(:firstName, '%'))
                OR LOWER(firstName) LIKE LOWER(CONCAT('%', :firstName))
                OR (ABS(LENGTH(firstName) - LENGTH(:firstName)) <= 2 AND LOWER(SUBSTRING(firstName, 1, 3)) = LOWER(SUBSTRING(:firstName, 1, 3)))
            )""")
            params["firstName"] = firstName
            params["firstNameFuzzy"] = "%$firstName%"
        }

        if (!lastName.isNullOrBlank()) {
            // More lenient matching for last names too
            nameConditions.add("""(
                LOWER(lastName) = LOWER(:lastName)
                OR LOWER(lastName) LIKE LOWER(:lastNameFuzzy)
                OR LOWER(:lastName) LIKE LOWER(CONCAT('%', lastName, '%'))
                OR LOWER(lastName) LIKE LOWER(CONCAT(:lastName, '%'))
                OR LOWER(lastName) LIKE LOWER(CONCAT('%', :lastName))
                OR (ABS(LENGTH(lastName) - LENGTH(:lastName)) <= 2 AND LOWER(SUBSTRING(lastName, 1, 3)) = LOWER(SUBSTRING(:lastName, 1, 3)))
            )""")
            params["lastName"] = lastName
            params["lastNameFuzzy"] = "%$lastName%"
        }

        // Use OR logic for name matching to be more flexible
        if (nameConditions.isNotEmpty()) {
            query.append(" AND (").append(nameConditions.joinToString(" OR ")).append(")")
        }

        if (dateOfBirth != null) {
            query.append(" AND dateOfBirth = :dateOfBirth")
            params["dateOfBirth"] = dateOfBirth
        }

        return find(query.toString(), params).list()
    }

    /**
     * Find potential duplicates based on contact information
     */
    fun findPotentialDuplicatesByContact(phoneNumber: String?, email: String?): List<Patient> {
        if (phoneNumber.isNullOrBlank() && email.isNullOrBlank()) {
            return emptyList()
        }

        val query = StringBuilder("""
            SELECT DISTINCT p FROM Patient p 
            LEFT JOIN PatientContact c ON c.patient = p 
            WHERE p.softDeleted = false
        """.trimIndent())

        val params = mutableMapOf<String, Any>()
        val conditions = mutableListOf<String>()

        if (!phoneNumber.isNullOrBlank()) {
            conditions.add("c.phoneNumber LIKE :phone OR c.mobileNumber LIKE :phone")
            params["phone"] = "%$phoneNumber%"
        }

        if (!email.isNullOrBlank()) {
            conditions.add("LOWER(c.email) = LOWER(:email)")
            params["email"] = email
        }

        if (conditions.isNotEmpty()) {
            query.append(" AND (").append(conditions.joinToString(" OR ")).append(")")
        }

        return find(query.toString(), params).list()
    }

    /**
     * Find potential duplicates based on identifier
     */
    fun findPotentialDuplicatesByIdentifier(
        identifierType: IdentifierType?,
        identifierNumber: String?
    ): List<Patient> {
        if (identifierType == null || identifierNumber.isNullOrBlank()) {
            return emptyList()
        }

        return find(
            "identifierType = ?1 AND identifierNumber = ?2 AND softDeleted = false",
            identifierType, identifierNumber
        ).list()
    }

    /**
     * Find potential duplicates based on ABHA number
     */
    fun findPotentialDuplicatesByAbha(abhaNumber: String?): List<Patient> {
        if (abhaNumber.isNullOrBlank()) {
            return emptyList()
        }

        val query = """
            SELECT DISTINCT p FROM Patient p
            LEFT JOIN PatientAbha a ON a.patient = p
            WHERE p.softDeleted = false AND a.abhaNumber = :abhaNumber
        """.trimIndent()

        return find(query, mapOf("abhaNumber" to abhaNumber)).list()
    }

    /**
     * Find patients for batch duplicate detection
     */
    fun findPatientsForBatchDetection(
        facilityId: String?,
        dateFrom: OffsetDateTime?,
        dateTo: OffsetDateTime?,
        page: Int,
        size: Int
    ): List<Patient> {
        val query = StringBuilder("softDeleted = false")
        val params = mutableMapOf<String, Any>()

        if (!facilityId.isNullOrBlank()) {
            query.append(" AND facilityId = :facilityId")
            params["facilityId"] = facilityId
        }

        if (dateFrom != null) {
            query.append(" AND registrationDate >= :dateFrom")
            params["dateFrom"] = dateFrom
        }

        if (dateTo != null) {
            query.append(" AND registrationDate <= :dateTo")
            params["dateTo"] = dateTo
        }

        query.append(" ORDER BY registrationDate DESC")

        return find(query.toString(), params)
            .page(Page.of(page, size))
            .list()
    }
}
